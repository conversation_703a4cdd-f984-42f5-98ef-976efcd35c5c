# TenantDB - Custom Event-Based ORM

A lightweight, custom Object-Relational Mapping (ORM) library designed specifically for event-based database systems. TenantDB provides a clean, intuitive API for working with event-sourced data while maintaining high performance and flexibility.

## 🏗️ Architecture Overview

TenantDB follows a layered architecture combining several proven design patterns:

### Core Design Patterns

1. **Event Sourcing Pattern**
   - All data changes are stored as immutable events
   - Current state is derived by replaying events
   - Full audit trail and temporal queries support

2. **Repository Pattern**
   - Abstracts data access logic
   - Provides a consistent interface for data operations
   - Enables easy testing and mocking

3. **Unit of Work Pattern**
   - Manages transactions and change tracking
   - Ensures data consistency across operations
   - Optimizes database interactions

4. **Command Query Responsibility Segregation (CQRS)**
   - Separates read and write operations
   - Optimizes performance for different access patterns
   - Enables independent scaling

### Key Components

- **Event Store**: Core storage mechanism for events
- **Aggregate Root**: Domain entities that maintain consistency boundaries
- **Event Handlers**: Process and apply events to rebuild state
- **Query Projections**: Optimized read models for different use cases
- **Snapshot Store**: Performance optimization for large event streams

## 🚀 Features

- **Event-First Design**: Built from the ground up for event-based systems
- **Type Safety**: Full C# type safety with compile-time checking
- **Async/Await Support**: Modern asynchronous programming patterns
- **Dependency Injection**: Full integration with Microsoft.Extensions.DependencyInjection
- **Logging Integration**: Comprehensive logging with Microsoft.Extensions.Logging
- **High Performance**: Optimized for both read and write operations
- **Testable**: Designed with testing in mind, easy to mock and unit test

## 📦 Installation

This library is designed to be used as an internal SDK. Add the project reference to your solution:

```bash
dotnet add reference path/to/TenantDB.Core/TenantDB.Core.csproj
```

## 🔧 Quick Start

```csharp
// Configure services
services.AddTenantDB(options =>
{
    options.ConnectionString = "your-connection-string";
    options.EnableSnapshots = true;
});

// Define your aggregate
public class UserAggregate : AggregateRoot
{
    public string Name { get; private set; }
    public string Email { get; private set; }
    
    public void CreateUser(string name, string email)
    {
        var @event = new UserCreatedEvent(Id, name, email);
        ApplyEvent(@event);
    }
}

// Use in your application
public class UserService
{
    private readonly IRepository<UserAggregate> _repository;
    private readonly IUnitOfWork _unitOfWork;
    
    public async Task<UserAggregate> CreateUserAsync(string name, string email)
    {
        var user = new UserAggregate();
        user.CreateUser(name, email);
        
        await _repository.SaveAsync(user);
        await _unitOfWork.CommitAsync();
        
        return user;
    }
}
```

## 🧪 Testing

The project includes comprehensive testing infrastructure:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions
- **Performance Tests**: Ensure scalability requirements

Run tests:
```bash
dotnet test
```

## 📁 Project Structure

```
TenantDB/
├── TenantDB.Core/              # Main library
│   ├── Aggregates/             # Aggregate root implementations
│   ├── Events/                 # Event definitions and handlers
│   ├── Repositories/           # Repository implementations
│   ├── Storage/                # Event store and snapshot store
│   ├── Queries/                # Query projections and handlers
│   └── Configuration/          # DI and configuration
├── TenantDB.Tests/             # Test project
└── README.md                   # This file
```

## 🎯 Goals

- **Simplicity**: Easy to understand and use API
- **Performance**: Optimized for high-throughput scenarios
- **Reliability**: Battle-tested patterns and comprehensive testing
- **Flexibility**: Extensible architecture for custom requirements
- **Maintainability**: Clean code with clear separation of concerns

## 🔄 Development Status

This project is in active development. Current focus areas:

- [ ] Core event store implementation
- [ ] Repository pattern implementation
- [ ] Unit of work pattern
- [ ] Query projection system
- [ ] Snapshot optimization
- [ ] Performance benchmarking
- [ ] Documentation and examples

## 🤝 Contributing

This is an internal project. Please follow the established coding standards and ensure all tests pass before submitting changes.

## 📄 License

Internal use only.
