{"format": 1, "restore": {"/home/<USER>/projects/whatson2/tenantdb/TenantDB.Tests/TenantDB.Tests.csproj": {}}, "projects": {"/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/TenantDB.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/TenantDB.Core.csproj", "projectName": "TenantDB.Core", "projectPath": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/TenantDB.Core.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"/var/snap/dotnet/common/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/var/snap/dotnet/common/dotnet/sdk/9.0.108/PortableRuntimeIdentifierGraph.json"}}}, "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Tests/TenantDB.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Tests/TenantDB.Tests.csproj", "projectName": "TenantDB.Tests", "projectPath": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Tests/TenantDB.Tests.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Tests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"/var/snap/dotnet/common/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/TenantDB.Core.csproj": {"projectPath": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/TenantDB.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[8.5.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.6.0, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.4.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.4.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/var/snap/dotnet/common/dotnet/sdk/9.0.108/PortableRuntimeIdentifierGraph.json"}}}}}