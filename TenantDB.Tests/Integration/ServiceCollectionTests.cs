using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using TenantDB.Core.Configuration;
using Xunit;

namespace TenantDB.Tests.Integration
{
    public class ServiceCollectionTests
    {
        [Fact]
        public void AddTenantDB_ShouldRegisterServices()
        {
            // Arrange
            var services = new ServiceCollection();
            
            // Act
            services.AddTenantDB(options =>
            {
                options.ConnectionString = "test-connection";
                options.EnableSnapshots = true;
                options.EnableDetailedLogging = true;
            });
            
            var serviceProvider = services.BuildServiceProvider();
            
            // Assert
            var options = serviceProvider.GetService<TenantDBOptions>();
            options.Should().NotBeNull();
            options!.ConnectionString.Should().Be("test-connection");
            options.EnableSnapshots.Should().BeTrue();
            options.EnableDetailedLogging.Should().BeTrue();
        }
        
        [Fact]
        public void AddTenantDB_WithConnectionString_ShouldSetConnectionString()
        {
            // Arrange
            var services = new ServiceCollection();
            const string connectionString = "test-connection-string";
            
            // Act
            services.AddTenantDB(connectionString);
            var serviceProvider = services.BuildServiceProvider();
            
            // Assert
            var options = serviceProvider.GetService<TenantDBOptions>();
            options.Should().NotBeNull();
            options!.ConnectionString.Should().Be(connectionString);
        }
    }
}
