using System;
using System.Linq;
using FluentAssertions;
using TenantDB.Tests.TestUtilities;
using Xunit;

namespace TenantDB.Tests.Unit
{
    public class AggregateRootTests
    {
        [Fact]
        public void Constructor_ShouldInitializeWithNewId()
        {
            // Act
            var aggregate = new TestAggregate();
            
            // Assert
            aggregate.Id.Should().NotBe(Guid.Empty);
            aggregate.Version.Should().Be(0);
            aggregate.GetUncommittedEvents().Should().BeEmpty();
        }
        
        [Fact]
        public void Constructor_WithId_ShouldInitializeWithSpecificId()
        {
            // Arrange
            var id = Guid.NewGuid();
            
            // Act
            var aggregate = new TestAggregate(id);
            
            // Assert
            aggregate.Id.Should().Be(id);
            aggregate.Version.Should().Be(0);
            aggregate.GetUncommittedEvents().Should().BeEmpty();
        }
        
        [Fact]
        public void ApplyEvent_ShouldIncrementVersionAndAddToUncommittedEvents()
        {
            // Arrange
            var aggregate = new TestAggregate();
            
            // Act
            aggregate.ChangeName("Test Name");
            
            // Assert
            aggregate.Version.Should().Be(1);
            aggregate.Name.Should().Be("Test Name");
            aggregate.GetUncommittedEvents().Should().HaveCount(1);
            
            var @event = aggregate.GetUncommittedEvents().First();
            @event.AggregateId.Should().Be(aggregate.Id);
            @event.Version.Should().Be(1);
        }
        
        [Fact]
        public void MarkEventsAsCommitted_ShouldClearUncommittedEvents()
        {
            // Arrange
            var aggregate = new TestAggregate();
            aggregate.ChangeName("Test Name");
            aggregate.IncrementCounter();
            
            // Act
            aggregate.MarkEventsAsCommitted();
            
            // Assert
            aggregate.GetUncommittedEvents().Should().BeEmpty();
            aggregate.Version.Should().Be(2); // Version should remain
            aggregate.Name.Should().Be("Test Name"); // State should remain
            aggregate.Counter.Should().Be(1); // State should remain
        }
        
        [Fact]
        public void LoadFromHistory_ShouldReplayEventsAndSetVersion()
        {
            // Arrange
            var aggregateId = Guid.NewGuid();
            var events = new[]
            {
                new TestEvent(aggregateId, 1, "NameChanged:Historical Name"),
                new TestEvent(aggregateId, 2, "CounterIncremented"),
                new TestEvent(aggregateId, 3, "CounterIncremented")
            };
            
            var aggregate = new TestAggregate(aggregateId);
            
            // Act
            aggregate.LoadFromHistory(events);
            
            // Assert
            aggregate.Version.Should().Be(3);
            aggregate.Name.Should().Be("Historical Name");
            aggregate.Counter.Should().Be(2);
            aggregate.GetUncommittedEvents().Should().BeEmpty(); // History events should not be uncommitted
        }
    }
}
