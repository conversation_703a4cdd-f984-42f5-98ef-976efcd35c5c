using System;
using TenantDB.Core.Events;

namespace TenantDB.Tests.TestUtilities
{
    /// <summary>
    /// Test event for unit testing
    /// </summary>
    public class TestEvent : BaseEvent
    {
        public string Data { get; }
        
        public TestEvent(Guid aggregateId, long version, string data) 
            : base(aggregateId, version)
        {
            Data = data;
        }
        
        public TestEvent(Guid eventId, Guid aggregateId, long version, DateTimeOffset timestamp, string eventType, string data)
            : base(eventId, aggregateId, version, timestamp, eventType)
        {
            Data = data;
        }
    }
}
