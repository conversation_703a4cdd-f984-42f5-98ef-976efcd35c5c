using System;
using TenantDB.Core.Aggregates;

namespace TenantDB.Tests.TestUtilities
{
    /// <summary>
    /// Test aggregate for unit testing
    /// </summary>
    public class TestAggregate : AggregateRoot
    {
        public string Name { get; private set; } = string.Empty;
        public int Counter { get; private set; }
        
        public TestAggregate() : base()
        {
        }
        
        public TestAggregate(Guid id) : base(id)
        {
        }
        
        public void ChangeName(string newName)
        {
            var @event = new TestEvent(Id, Version + 1, $"NameChanged:{newName}");
            ApplyEvent(@event);
        }
        
        public void IncrementCounter()
        {
            var @event = new TestEvent(Id, Version + 1, "CounterIncremented");
            ApplyEvent(@event);
        }
        
        // Event handlers
        public void Apply(TestEvent @event)
        {
            if (@event.Data.StartsWith("NameChanged:"))
            {
                Name = @event.Data.Substring("NameChanged:".Length);
            }
            else if (@event.Data == "CounterIncremented")
            {
                Counter++;
            }
        }
    }
}
