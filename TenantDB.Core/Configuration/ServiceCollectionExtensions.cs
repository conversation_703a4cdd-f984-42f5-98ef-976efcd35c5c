using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TenantDB.Core.Interfaces;

namespace TenantDB.Core.Configuration
{
    /// <summary>
    /// Extension methods for configuring TenantDB services
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Adds TenantDB services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configureOptions">Configuration action for TenantDB options</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddTenantDB(this IServiceCollection services, Action<TenantDBOptions>? configureOptions = null)
        {
            var options = new TenantDBOptions();
            configureOptions?.Invoke(options);
            
            services.AddSingleton(options);
            
            // Register core interfaces (implementations will be added later)
            // services.AddScoped<IEventStore, EventStore>();
            // services.AddScoped<IUnitOfWork, UnitOfWork>();
            // services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
            
            // Add logging if not already configured
            services.AddLogging(builder =>
            {
                if (options.EnableDetailedLogging)
                {
                    builder.SetMinimumLevel(LogLevel.Debug);
                }
            });
            
            return services;
        }
        
        /// <summary>
        /// Adds TenantDB services with a connection string
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="connectionString">The connection string</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddTenantDB(this IServiceCollection services, string connectionString)
        {
            return services.AddTenantDB(options =>
            {
                options.ConnectionString = connectionString;
            });
        }
    }
}
