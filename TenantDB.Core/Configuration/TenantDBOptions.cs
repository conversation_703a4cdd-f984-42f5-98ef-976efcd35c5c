namespace TenantDB.Core.Configuration
{
    /// <summary>
    /// Configuration options for TenantDB
    /// </summary>
    public class TenantDBOptions
    {
        /// <summary>
        /// Connection string for the event store
        /// </summary>
        public string ConnectionString { get; set; } = string.Empty;
        
        /// <summary>
        /// Whether to enable snapshot optimization
        /// </summary>
        public bool EnableSnapshots { get; set; } = true;
        
        /// <summary>
        /// Number of events after which to create a snapshot
        /// </summary>
        public int SnapshotFrequency { get; set; } = 100;
        
        /// <summary>
        /// Maximum number of events to load when rebuilding from snapshots
        /// </summary>
        public int MaxEventsToLoad { get; set; } = 1000;
        
        /// <summary>
        /// Timeout for database operations in seconds
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;
        
        /// <summary>
        /// Whether to enable detailed logging
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = false;
    }
}
