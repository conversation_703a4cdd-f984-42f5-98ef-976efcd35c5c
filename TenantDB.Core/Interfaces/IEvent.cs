using System;

namespace TenantDB.Core.Interfaces
{
    /// <summary>
    /// Represents a domain event in the event store
    /// </summary>
    public interface IEvent
    {
        /// <summary>
        /// Unique identifier for the event
        /// </summary>
        Guid EventId { get; }
        
        /// <summary>
        /// The aggregate ID this event belongs to
        /// </summary>
        Guid AggregateId { get; }
        
        /// <summary>
        /// The version of the aggregate when this event was created
        /// </summary>
        long Version { get; }
        
        /// <summary>
        /// When the event occurred
        /// </summary>
        DateTimeOffset Timestamp { get; }
        
        /// <summary>
        /// The type name of the event for serialization
        /// </summary>
        string EventType { get; }
    }
}
