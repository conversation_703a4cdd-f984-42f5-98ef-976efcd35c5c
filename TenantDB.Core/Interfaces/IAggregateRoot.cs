using System;
using System.Collections.Generic;

namespace TenantDB.Core.Interfaces
{
    /// <summary>
    /// Represents an aggregate root in the domain model
    /// </summary>
    public interface IAggregateRoot
    {
        /// <summary>
        /// Unique identifier for the aggregate
        /// </summary>
        Guid Id { get; }
        
        /// <summary>
        /// Current version of the aggregate
        /// </summary>
        long Version { get; }
        
        /// <summary>
        /// Gets all uncommitted events for this aggregate
        /// </summary>
        IReadOnlyList<IEvent> GetUncommittedEvents();
        
        /// <summary>
        /// Marks all events as committed
        /// </summary>
        void MarkEventsAsCommitted();
        
        /// <summary>
        /// Loads the aggregate from a sequence of events
        /// </summary>
        /// <param name="events">Historical events to replay</param>
        void LoadFromHistory(IEnumerable<IEvent> events);
    }
}
