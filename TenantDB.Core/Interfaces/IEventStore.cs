using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace TenantDB.Core.Interfaces
{
    /// <summary>
    /// Interface for the event store
    /// </summary>
    public interface IEventStore
    {
        /// <summary>
        /// Saves events to the store
        /// </summary>
        /// <param name="aggregateId">The aggregate ID</param>
        /// <param name="events">Events to save</param>
        /// <param name="expectedVersion">Expected version for optimistic concurrency</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SaveEventsAsync(Guid aggregateId, IEnumerable<IEvent> events, long expectedVersion, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Gets events for an aggregate
        /// </summary>
        /// <param name="aggregateId">The aggregate ID</param>
        /// <param name="fromVersion">Starting version (inclusive)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Events for the aggregate</returns>
        Task<IEnumerable<IEvent>> GetEventsAsync(Guid aggregateId, long fromVersion = 0, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Gets events for an aggregate up to a specific version
        /// </summary>
        /// <param name="aggregateId">The aggregate ID</param>
        /// <param name="fromVersion">Starting version (inclusive)</param>
        /// <param name="toVersion">Ending version (inclusive)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Events for the aggregate within the version range</returns>
        Task<IEnumerable<IEvent>> GetEventsAsync(Guid aggregateId, long fromVersion, long toVersion, CancellationToken cancellationToken = default);
    }
}
