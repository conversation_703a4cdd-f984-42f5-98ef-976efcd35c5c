using System;
using System.Threading;
using System.Threading.Tasks;

namespace TenantDB.Core.Interfaces
{
    /// <summary>
    /// Unit of Work pattern for managing transactions
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        /// <summary>
        /// Commits all changes in the current unit of work
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        Task CommitAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Rolls back all changes in the current unit of work
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        Task RollbackAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Indicates whether the unit of work has been committed
        /// </summary>
        bool IsCommitted { get; }
        
        /// <summary>
        /// Indicates whether the unit of work has been rolled back
        /// </summary>
        bool IsRolledBack { get; }
    }
}
