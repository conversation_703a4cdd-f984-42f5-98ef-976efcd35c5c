using System;
using System.Threading;
using System.Threading.Tasks;

namespace TenantDB.Core.Interfaces
{
    /// <summary>
    /// Repository interface for aggregate roots
    /// </summary>
    /// <typeparam name="T">The aggregate root type</typeparam>
    public interface IRepository<T> where T : class, IAggregateRoot
    {
        /// <summary>
        /// Gets an aggregate by its ID
        /// </summary>
        /// <param name="id">The aggregate ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The aggregate or null if not found</returns>
        Task<T?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Saves an aggregate to the event store
        /// </summary>
        /// <param name="aggregate">The aggregate to save</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task SaveAsync(T aggregate, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Gets an aggregate at a specific version
        /// </summary>
        /// <param name="id">The aggregate ID</param>
        /// <param name="version">The version to retrieve</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The aggregate at the specified version</returns>
        Task<T?> GetByIdAndVersionAsync(Guid id, long version, CancellationToken cancellationToken = default);
    }
}
