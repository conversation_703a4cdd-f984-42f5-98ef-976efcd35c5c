using System;
using TenantDB.Core.Interfaces;

namespace TenantDB.Core.Events
{
    /// <summary>
    /// Base implementation of an event
    /// </summary>
    public abstract class BaseEvent : IEvent
    {
        /// <inheritdoc />
        public Guid EventId { get; }
        
        /// <inheritdoc />
        public Guid AggregateId { get; }
        
        /// <inheritdoc />
        public long Version { get; }
        
        /// <inheritdoc />
        public DateTimeOffset Timestamp { get; }
        
        /// <inheritdoc />
        public string EventType { get; }
        
        /// <summary>
        /// Initializes a new event
        /// </summary>
        /// <param name="aggregateId">The aggregate ID</param>
        /// <param name="version">The aggregate version</param>
        protected BaseEvent(Guid aggregateId, long version)
        {
            EventId = Guid.NewGuid();
            AggregateId = aggregateId;
            Version = version;
            Timestamp = DateTimeOffset.UtcNow;
            EventType = GetType().Name;
        }
        
        /// <summary>
        /// Initializes an event from storage
        /// </summary>
        /// <param name="eventId">The event ID</param>
        /// <param name="aggregateId">The aggregate ID</param>
        /// <param name="version">The aggregate version</param>
        /// <param name="timestamp">When the event occurred</param>
        /// <param name="eventType">The event type name</param>
        protected BaseEvent(Guid eventId, Guid aggregateId, long version, DateTimeOffset timestamp, string eventType)
        {
            EventId = eventId;
            AggregateId = aggregateId;
            Version = version;
            Timestamp = timestamp;
            EventType = eventType;
        }
    }
}
