using System;
using System.Collections.Generic;
using System.Linq;
using TenantDB.Core.Interfaces;

namespace TenantDB.Core.Aggregates
{
    /// <summary>
    /// Base class for aggregate roots
    /// </summary>
    public abstract class AggregateRoot : IAggregateRoot
    {
        private readonly List<IEvent> _uncommittedEvents = new();
        
        /// <inheritdoc />
        public Guid Id { get; protected set; }
        
        /// <inheritdoc />
        public long Version { get; protected set; }
        
        /// <summary>
        /// Initializes a new aggregate root with a new ID
        /// </summary>
        protected AggregateRoot()
        {
            Id = Guid.NewGuid();
            Version = 0;
        }
        
        /// <summary>
        /// Initializes a new aggregate root with a specific ID
        /// </summary>
        /// <param name="id">The aggregate ID</param>
        protected AggregateRoot(Guid id)
        {
            Id = id;
            Version = 0;
        }
        
        /// <inheritdoc />
        public IReadOnlyList<IEvent> GetUncommittedEvents()
        {
            return _uncommittedEvents.AsReadOnly();
        }
        
        /// <inheritdoc />
        public void MarkEventsAsCommitted()
        {
            _uncommittedEvents.Clear();
        }
        
        /// <inheritdoc />
        public void LoadFromHistory(IEnumerable<IEvent> events)
        {
            foreach (var @event in events.OrderBy(e => e.Version))
            {
                ApplyEvent(@event, false);
                Version = @event.Version;
            }
        }
        
        /// <summary>
        /// Applies an event to the aggregate
        /// </summary>
        /// <param name="event">The event to apply</param>
        protected void ApplyEvent(IEvent @event)
        {
            ApplyEvent(@event, true);
        }
        
        /// <summary>
        /// Applies an event to the aggregate
        /// </summary>
        /// <param name="event">The event to apply</param>
        /// <param name="isNew">Whether this is a new event or from history</param>
        private void ApplyEvent(IEvent @event, bool isNew)
        {
            // Use reflection to find and call the appropriate Apply method
            var method = GetType().GetMethod("Apply", new[] { @event.GetType() });
            method?.Invoke(this, new object[] { @event });
            
            if (isNew)
            {
                Version++;
                _uncommittedEvents.Add(@event);
            }
        }
    }
}
