using System;

namespace TenantDB.Core.Exceptions
{
    /// <summary>
    /// Exception thrown when a concurrency conflict occurs
    /// </summary>
    public class ConcurrencyException : Exception
    {
        public Guid AggregateId { get; }
        public long ExpectedVersion { get; }
        public long ActualVersion { get; }
        
        public ConcurrencyException(Guid aggregateId, long expectedVersion, long actualVersion)
            : base($"Concurrency conflict for aggregate {aggregateId}. Expected version {expectedVersion}, but actual version is {actualVersion}")
        {
            AggregateId = aggregateId;
            ExpectedVersion = expectedVersion;
            ActualVersion = actualVersion;
        }
        
        public ConcurrencyException(Guid aggregateId, long expectedVersion, long actualVersion, Exception innerException)
            : base($"Concurrency conflict for aggregate {aggregateId}. Expected version {expectedVersion}, but actual version is {actualVersion}", innerException)
        {
            AggregateId = aggregateId;
            ExpectedVersion = expectedVersion;
            ActualVersion = actualVersion;
        }
    }
}
