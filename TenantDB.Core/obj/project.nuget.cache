{"version": 2, "dgSpecHash": "7B3TZYJw+78=", "success": true, "projectFilePath": "/home/<USER>/projects/whatson2/tenantdb/TenantDB.Core/TenantDB.Core.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/9.0.7/microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/9.0.7/microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging/9.0.7/microsoft.extensions.logging.9.0.7.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/9.0.7/microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.options/9.0.7/microsoft.extensions.options.9.0.7.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.7/microsoft.extensions.primitives.9.0.7.nupkg.sha512", "/home/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/9.0.7/system.diagnostics.diagnosticsource.9.0.7.nupkg.sha512", "/home/<USER>/.nuget/packages/system.io.pipelines/9.0.7/system.io.pipelines.9.0.7.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.encodings.web/9.0.7/system.text.encodings.web.9.0.7.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.json/9.0.7/system.text.json.9.0.7.nupkg.sha512"], "logs": []}