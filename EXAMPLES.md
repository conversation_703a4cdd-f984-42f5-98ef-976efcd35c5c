# TenantDB Usage Examples

This document provides examples of how to use the TenantDB custom ORM for event-based databases.

## Basic Usage

### 1. Define Your Events

```csharp
using TenantDB.Core.Events;

public class UserCreatedEvent : BaseEvent
{
    public string Name { get; }
    public string Email { get; }
    
    public UserCreatedEvent(Guid aggregateId, long version, string name, string email) 
        : base(aggregateId, version)
    {
        Name = name;
        Email = email;
    }
}

public class UserEmailChangedEvent : BaseEvent
{
    public string NewEmail { get; }
    
    public UserEmailChangedEvent(Guid aggregateId, long version, string newEmail) 
        : base(aggregateId, version)
    {
        NewEmail = newEmail;
    }
}
```

### 2. Create Your Aggregate

```csharp
using TenantDB.Core.Aggregates;

public class UserAggregate : AggregateRoot
{
    public string Name { get; private set; } = string.Empty;
    public string Email { get; private set; } = string.Empty;
    public bool IsActive { get; private set; }
    
    public UserAggregate() : base() { }
    public UserAggregate(Guid id) : base(id) { }
    
    // Commands
    public void CreateUser(string name, string email)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Name cannot be empty", nameof(name));
            
        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be empty", nameof(email));
            
        var @event = new UserCreatedEvent(Id, Version + 1, name, email);
        ApplyEvent(@event);
    }
    
    public void ChangeEmail(string newEmail)
    {
        if (string.IsNullOrWhiteSpace(newEmail))
            throw new ArgumentException("Email cannot be empty", nameof(newEmail));
            
        if (Email == newEmail)
            return; // No change needed
            
        var @event = new UserEmailChangedEvent(Id, Version + 1, newEmail);
        ApplyEvent(@event);
    }
    
    // Event Handlers
    public void Apply(UserCreatedEvent @event)
    {
        Name = @event.Name;
        Email = @event.Email;
        IsActive = true;
    }
    
    public void Apply(UserEmailChangedEvent @event)
    {
        Email = @event.NewEmail;
    }
}
```

### 3. Configure Services

```csharp
using Microsoft.Extensions.DependencyInjection;
using TenantDB.Core.Configuration;

// In your Startup.cs or Program.cs
services.AddTenantDB(options =>
{
    options.ConnectionString = "your-event-store-connection-string";
    options.EnableSnapshots = true;
    options.SnapshotFrequency = 50;
    options.EnableDetailedLogging = true;
});
```

### 4. Use in Your Application

```csharp
public class UserService
{
    private readonly IRepository<UserAggregate> _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<UserService> _logger;
    
    public UserService(
        IRepository<UserAggregate> userRepository,
        IUnitOfWork unitOfWork,
        ILogger<UserService> logger)
    {
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }
    
    public async Task<UserAggregate> CreateUserAsync(string name, string email)
    {
        _logger.LogInformation("Creating user with name: {Name}, email: {Email}", name, email);
        
        var user = new UserAggregate();
        user.CreateUser(name, email);
        
        await _userRepository.SaveAsync(user);
        await _unitOfWork.CommitAsync();
        
        _logger.LogInformation("User created with ID: {UserId}", user.Id);
        return user;
    }
    
    public async Task<UserAggregate?> GetUserAsync(Guid userId)
    {
        return await _userRepository.GetByIdAsync(userId);
    }
    
    public async Task UpdateUserEmailAsync(Guid userId, string newEmail)
    {
        var user = await _userRepository.GetByIdAsync(userId);
        if (user == null)
        {
            throw new InvalidOperationException($"User with ID {userId} not found");
        }
        
        user.ChangeEmail(newEmail);
        
        await _userRepository.SaveAsync(user);
        await _unitOfWork.CommitAsync();
        
        _logger.LogInformation("User {UserId} email updated to {Email}", userId, newEmail);
    }
}
```

## Testing Examples

### Unit Testing Aggregates

```csharp
[Fact]
public void CreateUser_ShouldRaiseUserCreatedEvent()
{
    // Arrange
    var user = new UserAggregate();
    
    // Act
    user.CreateUser("John Doe", "<EMAIL>");
    
    // Assert
    user.Name.Should().Be("John Doe");
    user.Email.Should().Be("<EMAIL>");
    user.IsActive.Should().BeTrue();
    user.Version.Should().Be(1);
    
    var events = user.GetUncommittedEvents();
    events.Should().HaveCount(1);
    events.First().Should().BeOfType<UserCreatedEvent>();
}
```

### Integration Testing

```csharp
[Fact]
public async Task UserService_CreateAndRetrieve_ShouldWork()
{
    // Arrange
    var services = new ServiceCollection();
    services.AddTenantDB("test-connection-string");
    // Add your implementations here
    
    var serviceProvider = services.BuildServiceProvider();
    var userService = serviceProvider.GetRequiredService<UserService>();
    
    // Act
    var createdUser = await userService.CreateUserAsync("Test User", "<EMAIL>");
    var retrievedUser = await userService.GetUserAsync(createdUser.Id);
    
    // Assert
    retrievedUser.Should().NotBeNull();
    retrievedUser!.Name.Should().Be("Test User");
    retrievedUser.Email.Should().Be("<EMAIL>");
}
```

## Next Steps

1. Implement the `IEventStore` interface for your specific database
2. Implement the `IRepository<T>` interface
3. Implement the `IUnitOfWork` interface
4. Add query projections for read models
5. Implement snapshot functionality for performance optimization
6. Add more comprehensive error handling and validation
7. Create performance benchmarks and optimization

## Best Practices

- Keep aggregates small and focused
- Use meaningful event names that describe business actions
- Implement proper validation in aggregate methods
- Use dependency injection for all services
- Write comprehensive tests for your aggregates and services
- Consider implementing CQRS patterns for complex read scenarios
- Monitor performance and implement snapshots when needed
